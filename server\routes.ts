import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { createServer as createTCPServer } from "net";
import { storage } from "./storage";
import { userManager } from "./userManager";
// @ts-expect-error: JS import
import { SatelliteMessageDecoder } from "./gestioneSatellitari.js";
import { loginSchema, type WebSocketMessage, type SatelliteMessage } from "@shared/schema";
import { z } from "zod";
import multer from 'multer';
import type { Request } from 'express';
import { sanitizeMessages } from './sanitizeMessages';

const messageDecoder = new SatelliteMessageDecoder();
const upload = multer({ storage: multer.memoryStorage() });

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // WebSocket server for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });
  const clients = new Set<WebSocket>();

  wss.on('connection', (ws) => {
    clients.add(ws);
    // console.log('WebSocket client connected');

    ws.on('close', () => {
      clients.delete(ws);
      // console.log('WebSocket client disconnected');
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      clients.delete(ws);
    });
  });

  // Function to broadcast to all WebSocket clients
  function broadcast(message: WebSocketMessage) {
    const messageStr = JSON.stringify(message);
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageStr);
      }
    });
  }

  // TCP Server for satellite devices (port 8090)
  const tcpServer = createTCPServer((socket) => {
    console.log('Satellite device connected:', socket.remoteAddress);
    
    let deviceImei: string = "";
    let firstMessageSent = false;
    let lastMessage: SatelliteMessage | null = null;

    socket.on('data', async (data) => {
      try {
        const dataString = data.toString().trim();
        console.log('Received data from satellite:', dataString);
        
        // Skip HTTP requests that might accidentally connect to TCP port
        if (dataString.startsWith('GET ') || dataString.startsWith('POST ') || 
            dataString.startsWith('PUT ') || dataString.startsWith('DELETE ')) {
          console.log('Ignoring HTTP request on TCP port');
          socket.end('HTTP/1.1 400 Bad Request\r\n\r\nThis is a TCP port for satellite devices only\r\n');
          return;
        }
        
        // Decode the message using gestioneSatellitari
        let decodedMessage = messageDecoder.decodeMessage(data, deviceImei || "");
        // --- FORZA satelliteTimestamp come Date e ISO UTC ---
        if (decodedMessage && decodedMessage.satelliteTimestamp) {
          if (!(decodedMessage.satelliteTimestamp instanceof Date)) {
            decodedMessage.satelliteTimestamp = new Date(decodedMessage.satelliteTimestamp);
          }
          // Forza a stringa ISO UTC
          decodedMessage.satelliteTimestamp = decodedMessage.satelliteTimestamp.toISOString();
        }
        console.log('Decoded message:', decodedMessage);
        console.log('Message status:', decodedMessage?.status);
        
        // --- FILTRO COORDINATE DUPLICATE ---
        if (decodedMessage && lastMessage) {
          // Confronta solo se stesso IMEI e status "Fixed" o "No Fixed"
          if (
            decodedMessage.imei === lastMessage.imei &&
            (decodedMessage.status === 'Fixed' || decodedMessage.status === 'No Fixed') &&
            (lastMessage.status === 'Fixed' || lastMessage.status === 'No Fixed') &&
            decodedMessage.latitude === lastMessage.latitude &&
            decodedMessage.longitude === lastMessage.longitude
          ) {
            console.log('Messaggio ignorato: coordinate identiche al precedente.');
            return; // Non memorizzare né trasmettere
          }
        }
        // --- FINE FILTRO ---
        
        if (decodedMessage) {
          deviceImei = decodedMessage.imei;

          // No longer modifying message status - store raw messages
          firstMessageSent = true;

          // Store the message
          const savedMessage = await storage.addSatelliteMessage(decodedMessage);
          lastMessage = savedMessage;
          
          // Mark device as online
          await storage.setDeviceOnline(deviceImei || "");
          
          // Broadcast to WebSocket clients
          broadcast({
            type: 'new_message',
            data: savedMessage
          });
          
          // Broadcast device status update
          broadcast({
            type: 'device_status',
            data: {
              imei: deviceImei,
              online: true
            }
          });
          
          console.log(`Message from ${deviceImei} stored and broadcasted`);
        }
      } catch (error) {
        console.error('Error processing satellite message:', error);
      }
    });

    socket.on('close', async () => {
      console.log('Satellite device disconnected:', socket.remoteAddress);
      if (deviceImei && lastMessage) {
        // Usa la data dell'ultimo messaggio satellitare + 5 secondi per il messaggio End
        const lastSatTime = new Date(lastMessage.satelliteTimestamp);
        const endSatTime = new Date(lastSatTime.getTime() + 5000); // aggiungi 5 secondi
        const endMessage = { ...lastMessage, status: 'End', satelliteTimestamp: endSatTime };
        // Salva come nuovo messaggio con status End
        const savedEndMessage = await storage.addSatelliteMessage(endMessage);
        // Broadcast anche questo
        broadcast({
          type: 'new_message',
          data: savedEndMessage
        });
      }
      if (deviceImei) {
        // Processa il buffer retroattivo quando il dispositivo si disconnette
        const processedMessages = await storage.setDeviceOffline(deviceImei || "");

        // Invia tutti i messaggi processati dal buffer al frontend
        processedMessages.forEach(message => {
          broadcast({
            type: 'new_message',
            data: message
          });
        });

        broadcast({
          type: 'device_status',
          data: {
            imei: deviceImei,
            online: false
          }
        });
      }
    });

    socket.on('error', (error) => {
      console.error('TCP socket error:', error);
    });
  });

  // Start TCP server on port 8090
  tcpServer.listen(8090, '0.0.0.0', () => {
    console.log('TCP server listening on port 8090 for satellite devices');
  });

  // Authentication endpoint
  app.post('/api/auth/login', async (req, res) => {
    try {
      const { username, password } = loginSchema.parse(req.body);
      
      const user = await userManager.validateUser(username, password);
      if (!user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Convert CSV user to app user format
      const imeiArray = user.imeis.split(',').map(imei => imei.trim()).filter(Boolean);
      
      // Check if user already exists in memory storage
      let appUser = await storage.getUserByUsername(user.username);
      if (!appUser) {
        // Create user in memory storage for session management
        appUser = await storage.createUser({
          username: user.username,
          password: user.password,
          imeis: imeiArray,
          role: user.role || 'user',
        });
      }

      res.json({
        user: {
          id: appUser.id,
          username: appUser.username,
          imeis: appUser.imeis,
          role: appUser.role,
        }
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid request data' });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Registration endpoint
  app.post('/api/auth/register', async (req, res) => {
    try {
      const { username, password, imeis } = req.body;
      
      if (!username || !password || !imeis || !Array.isArray(imeis)) {
        return res.status(400).json({ message: 'Username, password, and imeis array are required' });
      }

      const validImeis = imeis.filter(imei => imei && imei.trim().length > 0);
      if (validImeis.length === 0) {
        return res.status(400).json({ message: 'At least one valid IMEI is required' });
      }

      // Check if user already exists in CSV
      const existingUser = await userManager.getUserByUsername(username);
      if (existingUser) {
        return res.status(409).json({ message: 'Username already exists' });
      }

      // Save to CSV file
      const csvUser = {
        username: username.trim(),
        password: password.trim(),
        imeis: validImeis.join(','),
        role: 'user',
      };
      await userManager.saveUser(csvUser);

      // Create user in memory storage for session management
      const appUser = await storage.createUser({
        username: csvUser.username,
        password: csvUser.password,
        imeis: validImeis,
        role: 'user',
      });

      res.status(201).json({
        user: {
          id: appUser.id,
          username: appUser.username,
          imeis: appUser.imeis,
          role: appUser.role,
        }
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get satellite messages for user's IMEIs
  app.get('/api/messages', async (req, res) => {
    try {
      const { userId, imei, startDate, endDate } = req.query;
      
      if (!userId) {
        return res.status(400).json({ message: 'User ID required' });
      }

      const user = await storage.getUser(parseInt(userId as string));
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      let imeis = user.imeis;
      
      // Filter by specific IMEI if provided
      if (imei && typeof imei === 'string') {
        // Admin can access any IMEI, regular users only their assigned IMEIs
        if (user.role !== 'admin' && !user.imeis.includes(imei)) {
          return res.status(403).json({ message: 'Access denied to this IMEI' });
        }
        imeis = [imei];
      }

      const start = startDate ? new Date(startDate as string) : undefined;
      const end = endDate ? new Date(endDate as string) : undefined;

      // LOG TEMPORANEO PER DEBUG DATE
      console.log('API /api/messages', {
        startDate, endDate,
        start: start ? start.toISOString() : null,
        end: end ? end.toISOString() : null
      });

      const messages = await storage.getSatelliteMessages(imeis, start, end);
      
      res.json(messages);
    } catch (error) {
      console.error('Error fetching messages:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get online devices for user's IMEIs
  app.get('/api/devices/online', async (req, res) => {
    try {
      const { userId } = req.query;
      
      if (!userId) {
        return res.status(400).json({ message: 'User ID required' });
      }

      const user = await storage.getUser(parseInt(userId as string));
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      const allOnlineDevices = await storage.getOnlineDevices();
      // Admin can see all online devices, regular users only their assigned IMEIs
      const userOnlineDevices = user.role === 'admin' 
        ? allOnlineDevices 
        : allOnlineDevices.filter(imei => user.imeis.includes(imei));
      
      res.json({
        onlineDevices: userOnlineDevices,
        totalOnline: userOnlineDevices.length
      });
    } catch (error) {
      console.error('Error fetching online devices:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });



  // Endpoint per ottenere tutti gli utenti (solo admin)
  app.get('/api/admin/users', async (_req, res) => {
    // In produzione: aggiungere controllo autenticazione/ruolo admin
    try {
      const users = await userManager.loadUsers();
      const result = users.map(u => ({
        username: u.username,
        role: u.role || 'user',
        imeis: u.imeis.split(',').map(i => i.trim()).filter(Boolean),
      }));
      res.json(result);
    } catch (error) {
      res.status(500).json({ message: 'Errore nel recupero utenti' });
    }
  });

  // Crea nuovo utente (admin)
  app.post('/api/admin/users', async (req, res) => {
    try {
      const { username, password, imeis, role } = req.body;
      if (!username || !password || !imeis || !Array.isArray(imeis) || !role) {
        return res.status(400).json({ message: 'Dati mancanti' });
      }
      // Controlla se esiste già
      const existing = await userManager.getUserByUsername(username);
      if (existing) return res.status(409).json({ message: 'Utente già esistente' });
      await userManager.saveUser({ username, password, imeis: imeis.join(','), role });
      res.status(201).json({ message: 'Utente creato' });
    } catch (error) {
      res.status(500).json({ message: 'Errore creazione utente' });
    }
  });

  // Modifica utente (admin)
  app.put('/api/admin/users/:username', async (req, res) => {
    try {
      const { username } = req.params;
      const { password, imeis, role } = req.body;
      const user = await userManager.getUserByUsername(username);
      if (!user) return res.status(404).json({ message: 'Utente non trovato' });
      await userManager.saveUser({
        username,
        password: password || user.password,
        imeis: imeis ? imeis.join(',') : user.imeis,
        role: role || user.role
      });
      res.json({ message: 'Utente aggiornato' });
    } catch (error) {
      res.status(500).json({ message: 'Errore aggiornamento utente' });
    }
  });

  // Elimina utente (admin)
  app.delete('/api/admin/users/:username', async (req, res) => {
    try {
      const { username } = req.params;
      const users = await userManager.loadUsers();
      const filtered = users.filter(u => u.username !== username);
      if (filtered.length === users.length) return res.status(404).json({ message: 'Utente non trovato' });
      // Sovrascrivi CSV senza l'utente
      const headers = 'username,password,imeis,role\n';
      const userLines = filtered.map(u => `${u.username},${u.password},"${u.imeis}",${u.role}`);
      const csvContent = headers + userLines.join('\n') + '\n';
      const fs = await import('fs');
      const path = await import('path');
      await fs.promises.writeFile(path.join(process.cwd(), 'users.csv'), csvContent, 'utf8');
      res.json({ message: 'Utente eliminato' });
    } catch (error) {
      console.error('Errore dettagliato eliminazione utente:', error);
      res.status(500).json({ message: 'Errore eliminazione utente' });
    }
  });

  // Endpoint per ottenere tutti gli IMEI con username (solo admin)
  app.get('/api/admin/imeis', async (_req, res) => {
    // In produzione: aggiungere controllo autenticazione/ruolo admin
    try {
      const users = await userManager.loadUsers();
      const imeiList: { imei: string; username: string; displayText: string }[] = [];
      
      for (const user of users) {
        const userImeis = user.imeis.split(',').map(i => i.trim()).filter(Boolean);
        for (const imei of userImeis) {
          // Formato: username - ultimi 4 caratteri dell'IMEI
          const last4 = imei.slice(-4);
          imeiList.push({
            imei,
            username: user.username,
            displayText: `${user.username} - ${last4}`
          });
        }
      }
      
      // Ordina per username, poi per IMEI
      imeiList.sort((a, b) => {
        if (a.username !== b.username) {
          return a.username.localeCompare(b.username);
        }
        return a.imei.localeCompare(b.imei);
      });
      
      res.json(imeiList);
    } catch (error) {
      console.error('Errore nel recupero IMEI:', error);
      res.status(500).json({ message: 'Errore nel recupero IMEI' });
    }
  });

  // Endpoint per esportazione messaggi (admin)
  app.get('/api/admin/messages/export', async (req, res) => {
    // In produzione: aggiungere controllo autenticazione/ruolo admin
    try {
      const { username, imei, format } = req.query;
      let imeis: string[] = [];
      if (username) {
        const user = await userManager.getUserByUsername(username as string);
        if (!user) return res.status(404).json({ message: 'Utente non trovato' });
        imeis = user.imeis.split(',').map(i => i.trim()).filter(Boolean);
      } else {
        // Tutti gli IMEI di tutti gli utenti
        const users = await userManager.loadUsers();
        imeis = Array.from(new Set(users.flatMap(u => u.imeis.split(',').map(i => i.trim()).filter(Boolean))));
      }
      if (imei) {
        imeis = imeis.filter(i => i === imei);
      }
      // Recupera i messaggi
      let messages = await storage.getSatelliteMessages(imeis);
      // Rimuovi id da ogni messaggio esportato
      const exportMessages = messages.map(({ id, ...rest }) => ({ ...rest }));
      if ((format as string)?.toLowerCase() === 'kml') {
        // Per KML, assicura che satelliteTimestamp sia un oggetto Date valido
        const kmlMessages = exportMessages.map((rest) => {
          let satTime = rest.satelliteTimestamp;
          if (!(satTime instanceof Date)) {
            satTime = new Date(satTime);
          }
          if (isNaN(satTime.getTime())) return null;
          return {
            ...rest,
            satelliteTimestamp: satTime
          };
        }).filter(Boolean);
        // Genera KML
        const kml = generateKml(kmlMessages);
        res.setHeader('Content-Disposition', 'attachment; filename="satellite-messages.kml"');
        res.setHeader('Content-Type', 'application/vnd.google-earth.kml+xml');
        return res.send(kml);
      } else {
        // Default: JSON
        res.setHeader('Content-Disposition', 'attachment; filename="satellite-messages.json"');
        res.json(exportMessages);
      }
    } catch (error) {
      console.error('Errore dettagliato esportazione messaggi:', error);
      res.status(500).json({ message: 'Errore esportazione messaggi' });
    }
  });

  // Endpoint per importazione messaggi (admin)
  app.post('/api/admin/messages/import', upload.single('file'), async (req: Request & { file?: Express.Multer.File }, res) => {
    // In produzione: aggiungere controllo autenticazione/ruolo admin
    try {
      if (!req.file) return res.status(400).json({ message: 'Nessun file caricato' });
      const { originalname, mimetype, buffer } = req.file;
      let messages = [];
      if (mimetype === 'application/json' || originalname.endsWith('.json')) {
        messages = JSON.parse(buffer.toString('utf-8'));
        // --- CONVERSIONE satelliteTimestamp in ISO UTC per ogni messaggio importato ---
        messages = messages.map((msg: any) => ({
          ...msg,
          satelliteTimestamp: msg.satelliteTimestamp ? new Date(msg.satelliteTimestamp).toISOString() : new Date().toISOString()
        }));
        // --- SANIFICAZIONE TRIPID ---
        messages = sanitizeMessages(messages);
      } else if (mimetype === 'application/vnd.google-earth.kml+xml' || originalname.endsWith('.kml')) {
        // Parsing KML semplice: estrai coordinate e dati base
        const xml2js = await import('xml2js');
        const parsed = await xml2js.parseStringPromise(buffer.toString('utf-8'));
        const placemarks = parsed.kml.Document[0].Placemark || [];
        messages = placemarks.map((p: any) => {
          const coords = p.Point[0].coordinates[0].split(',');
          // --- TENTA DI ESTRARRE LA DATA DAL DESCRIPTION, ALTRIMENTI USA ORA ---
          let date = new Date();
          let speed = 0;
          let direction = 0;
          let batteryPercentage = 0;
          if (p.description && p.description[0]) {
            const desc = p.description[0];
            // Data - cerca sia il formato ISO che quello locale
            const matchDate = desc.match(/Data: ([^<\n]+)/);
            if (matchDate && matchDate[1]) {
              const parsedDate = new Date(matchDate[1].trim());
              if (!isNaN(parsedDate.getTime())) date = parsedDate;
            }
            // Latitudine e Longitudine separate (non utilizzate nel parsing KML)
            // Velocità
            const matchSpeed = desc.match(/Velocità: (\d+(?:\.\d+)?) km\/h/);
            if (matchSpeed && matchSpeed[1]) {
              speed = parseFloat(matchSpeed[1]);
            }
            // Direzione
            const matchDirection = desc.match(/Direzione: (\d+(?:\.\d+)?)°/);
            if (matchDirection && matchDirection[1]) {
              direction = parseFloat(matchDirection[1]);
            }
            // Batteria
            const matchBattery = desc.match(/Batteria: (\d+)%/);
            if (matchBattery && matchBattery[1]) {
              batteryPercentage = parseInt(matchBattery[1], 10);
            }
          }
          
          // Usa le coordinate estratte dal description se disponibili, altrimenti usa quelle del Point
          let latitude = parseFloat(coords[1]);
          let longitude = parseFloat(coords[0]);
          
          if (p.description && p.description[0]) {
            const desc = p.description[0];
            const matchLat = desc.match(/Lat: ([^<\n]+)/);
            const matchLng = desc.match(/Lng: ([^<\n]+)/);
            if (matchLat && matchLat[1]) {
              latitude = parseFloat(matchLat[1].trim());
            }
            if (matchLng && matchLng[1]) {
              longitude = parseFloat(matchLng[1].trim());
            }
          }
          
          return {
            imei: p.name[0].split(' - ')[0],
            status: p.name[0].split(' - ')[1],
            satelliteTimestamp: date.toISOString(),
            latitude,
            longitude,
            speed,
            direction,
            batteryPercentage,
          };
        });
        // --- SANIFICAZIONE TRIPID anche per KML ---
        messages = sanitizeMessages(messages);
      } else {
        return res.status(400).json({ message: 'Formato file non supportato' });
      }
      // Salva i messaggi
      for (const msg of messages) {
        // Ensure all required properties are present and properly typed
        const messageToSave = {
          imei: msg.imei,
          status: msg.status,
          satelliteTimestamp: typeof msg.satelliteTimestamp === 'string'
            ? new Date(msg.satelliteTimestamp)
            : msg.satelliteTimestamp,
          latitude: typeof msg.latitude === 'number' ? msg.latitude : 0,
          longitude: typeof msg.longitude === 'number' ? msg.longitude : 0,
          speed: typeof msg.speed === 'number' ? msg.speed : 0,
          direction: typeof msg.direction === 'number' ? msg.direction : 0,
          batteryPercentage: typeof msg.batteryPercentage === 'number' ? msg.batteryPercentage : 0,
          tripId: msg.tripId || null
        };
        await storage.addSatelliteMessage(messageToSave);
      }
      res.json({ message: `Importati ${messages.length} messaggi` });
    } catch (error) {
      console.error('Errore dettagliato importazione messaggi:', error);
      res.status(500).json({ message: 'Errore importazione messaggi' });
    }
  });



  // Funzione di utilità per generare KML
  function generateKml(messages: any[]) {
    const placemarks = messages.map((m: any) => {
      const timestamp = (m.satelliteTimestamp instanceof Date ? m.satelliteTimestamp.toISOString() : new Date(m.satelliteTimestamp).toISOString());
      return `
      <Placemark>
        <name>${m.imei} - ${m.status}</name>
        <description><![CDATA[
          Data: ${timestamp}<br/>
          Lat: ${m.latitude}<br/>
          Lng: ${m.longitude}<br/>
          Velocità: ${m.speed} km/h<br/>
          Direzione: ${m.direction}°<br/>
          Batteria: ${m.batteryPercentage}%<br/>
          Stato: ${m.status}
        ]]></description>
        <Point><coordinates>${m.longitude},${m.latitude},0</coordinates></Point>
      </Placemark>
    `;
    }).join('\n');
    
    return `<?xml version="1.0" encoding="UTF-8"?>
      <kml xmlns="http://www.opengis.net/kml/2.2">
        <Document>
          <name>Satellite Messages</name>
          ${placemarks}
        </Document>
      </kml>`;
  }



  return httpServer;
}

