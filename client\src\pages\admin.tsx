import { useEffect, useRef } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";

export default function AdminPage() {
  const [, navigate] = useLocation();
  const { user, isLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [utenti, set<PERSON>tenti] = useState<{ username: string; role: string; imeis: string[] }[]>([]);
  const [loading, setLoading] = useState(true);
  const [errore, setErrore] = useState<string | null>(null);
  const [utenteSelezionato, setUtenteSelezionato] = useState<string>("");
  const [imeiSelezionato, setImeiSelezionato] = useState<string>("");
  const [formato, setFormato] = useState<string>("json");
  const exportBtnRef = useRef<HTMLButtonElement>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [editUser, setEditUser] = useState<{ username: string; role: string; imeis: string[] } | null>(null);
  const [formUser, setFormUser] = useState<{ username: string; password: string; role: string; imeis: string[] }>({ username: '', password: '', role: 'user', imeis: [''] });
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [deleteUser, setDeleteUser] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // IMEI dinamici in base all'utente selezionato
  const imeiDisponibili = utenteSelezionato
    ? (utenti.find(u => u.username === utenteSelezionato)?.imeis || [])
    : Array.from(new Set(utenti.flatMap(u => u.imeis)));

  const handleExport = async () => {
    const params = new URLSearchParams();
    if (utenteSelezionato) params.append("username", utenteSelezionato);
    if (imeiSelezionato) params.append("imei", imeiSelezionato);
    if (formato) params.append("format", formato);
    const url = `/api/admin/messages/export?${params.toString()}`;
    exportBtnRef.current!.disabled = true;
    try {
      // Prima ottieni il numero di messaggi
      const countParams = new URLSearchParams();
      if (utenteSelezionato) countParams.append("username", utenteSelezionato);
      if (imeiSelezionato) countParams.append("imei", imeiSelezionato);
      const countUrl = `/api/admin/messages/export?${countParams.toString()}`;
      const countRes = await fetch(countUrl);
      let messageCount = 0;
      if (countRes.ok) {
        const messages = await countRes.json();
        messageCount = Array.isArray(messages) ? messages.length : 0;
      }
      
      // Poi esporta il file
      const res = await fetch(url);
      if (!res.ok) throw new Error("Errore durante l'esportazione");
      const blob = await res.blob();
      const a = document.createElement("a");
      a.href = URL.createObjectURL(blob);
      a.download = formato === "kml" ? "satellite-messages.kml" : "satellite-messages.json";
      document.body.appendChild(a);
      a.click();
      a.remove();
      
      // Mostra toast di successo per l'esportazione con il numero di messaggi
      toast({
        title: "Esportazione completata",
        description: `${messageCount} messaggi esportati in formato ${formato.toUpperCase()}`,
      });
    } catch (err) {
      // Mostra toast di errore per l'esportazione
      toast({
        title: "Errore di esportazione",
        description: "Errore durante l'esportazione del file",
        variant: "destructive",
      });
    } finally {
      exportBtnRef.current!.disabled = false;
    }
  };

  const handleImport = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Mostra toast di conferma prima dell'importazione
    const t = toast({
      title: "Importa file",
      description: `Vuoi importare il file "${file.name}"?`,
      persistent: true,
      action: (
        <div className="flex space-x-2">
          <button
            className="px-3 py-1 rounded bg-green-600 text-white hover:bg-green-700 text-sm"
            onClick={async () => {
              t.dismiss();
              const formData = new FormData();
              formData.append('file', file);
              try {
                const res = await fetch('/api/admin/messages/import', {
                  method: 'POST',
                  body: formData,
                });
                const data = await res.json();
                if (res.ok) {
                  // Invalida le cache dei messaggi per assicurarsi che i nuovi dati siano visibili
                  queryClient.invalidateQueries({ queryKey: ['/api/messages'] });

                  // Mostra toast di conferma
                  toast({
                    title: "Importazione completata",
                    description: data.message,
                  });
                } else {
                  // Mostra toast di errore
                  toast({
                    title: "Errore di importazione",
                    description: data.message || 'Errore sconosciuto',
                    variant: "destructive",
                  });
                }
              } catch (err) {
                // Mostra toast di errore
                toast({
                  title: "Errore di importazione",
                  description: 'Errore durante l\'importazione',
                  variant: "destructive",
                });
              }
            }}
          >
            Importa
          </button>
          <button
            className="px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600 text-sm"
            onClick={() => t.dismiss()}
          >
            Annulla
          </button>
        </div>
      ),
    });
    
    // Reset del file input
    e.target.value = '';
  };

  const openAddUser = () => {
    setEditUser(null);
    setFormUser({ username: '', password: '', role: 'user', imeis: [''] });
    setShowUserModal(true);
  };
  const openEditUser = (utente: { username: string; role: string; imeis: string[] }) => {
    setEditUser(utente);
    setFormUser({ username: utente.username, password: '', role: utente.role, imeis: utente.imeis.length ? utente.imeis : [''] });
    setShowUserModal(true);
  };
  const closeUserModal = () => {
    setShowUserModal(false);
    setFormError(null);
  };
  const handleFormChange = (field: string, value: string | string[], idx?: number) => {
    if (field === 'imeis' && typeof idx === 'number') {
      const newImeis = [...formUser.imeis];
      newImeis[idx] = value as string;
      setFormUser({ ...formUser, imeis: newImeis });
    } else if (field === 'imeis-add') {
      setFormUser({ ...formUser, imeis: [...formUser.imeis, ''] });
    } else if (field === 'imeis-remove' && typeof idx === 'number') {
      const newImeis = formUser.imeis.filter((_, i) => i !== idx);
      setFormUser({ ...formUser, imeis: newImeis.length ? newImeis : [''] });
    } else {
      setFormUser({ ...formUser, [field]: value });
    }
  };
  const handleUserSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);
    setFormError(null);
    try {
      const method = editUser ? 'PUT' : 'POST';
      const url = editUser ? `/api/admin/users/${encodeURIComponent(editUser.username)}` : '/api/admin/users';
      const body = {
        username: formUser.username,
        password: formUser.password || undefined,
        role: formUser.role,
        imeis: formUser.imeis.filter(i => i.trim().length > 0),
      };
      const res = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || 'Errore');
      // Aggiorna tabella
      setShowUserModal(false);
      setFormUser({ username: '', password: '', role: 'user', imeis: [''] });
      // Ricarica utenti
      setLoading(true);
      fetch("/api/admin/users")
        .then(res => res.json())
        .then(setUtenti)
        .finally(() => setLoading(false));
      // Invalida la cache degli IMEI per aggiornare il selettore nella pagina tracking
      console.log('Invalidando cache degli IMEI dopo modifica utente');
      queryClient.invalidateQueries({ queryKey: ['/api/admin/imeis'] });
      // Forza anche la ricarica delle query correlate
      queryClient.refetchQueries({ queryKey: ['/api/admin/imeis'] });
    } catch (err: any) {
      setFormError(err.message || 'Errore');
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!deleteUser) return;
    setDeleteLoading(true);
    setDeleteError(null);
    try {
      const res = await fetch(`/api/admin/users/${encodeURIComponent(deleteUser)}`, { method: 'DELETE' });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || 'Errore');
      setDeleteUser(null);
      // Ricarica utenti
      setLoading(true);
      fetch("/api/admin/users")
        .then(res => res.json())
        .then(setUtenti)
        .finally(() => setLoading(false));
      // Invalida la cache degli IMEI per aggiornare il selettore nella pagina tracking
      console.log('Invalidando cache degli IMEI dopo eliminazione utente:', deleteUser);
      queryClient.invalidateQueries({ queryKey: ['/api/admin/imeis'] });
      // Forza anche la ricarica delle query correlate
      queryClient.refetchQueries({ queryKey: ['/api/admin/imeis'] });
      // Aspetta un momento e poi forza nuovamente l'invalidazione per essere sicuri
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['/api/admin/imeis'] });
        queryClient.refetchQueries({ queryKey: ['/api/admin/imeis'] });
      }, 100);
    } catch (err: any) {
      setDeleteError(err.message || 'Errore');
    } finally {
      setDeleteLoading(false);
    }
  };

  useEffect(() => {
    if (!isLoading && (!user || user.role !== "admin")) {
      navigate("/login");
    }
  }, [isLoading, user, navigate]);

  useEffect(() => {
    setLoading(true);
    fetch("/api/admin/users")
      .then(res => {
        if (!res.ok) throw new Error("Errore nel recupero utenti");
        return res.json();
      })
      .then(data => {
        setUtenti(data);
        setErrore(null);
      })
      .catch(err => {
        setErrore(err.message || "Errore sconosciuto");
      })
      .finally(() => setLoading(false));
  }, []);

  // Mostra loading durante l'inizializzazione dell'autenticazione
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Caricamento...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <header className="border-b p-4 flex items-center justify-between bg-white">
        <h1 className="text-2xl font-bold text-gray-800">Pannello Amministratore</h1>
        <Button onClick={() => navigate("/")}>Torna alla Home</Button>
      </header>
      <main className="flex-1 p-6 space-y-8 bg-gray-50">
        {/* Gestione Utenti */}
        <section className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-700">Gestione Utenti</h2>
            <Button variant="outline" className="bg-[#005c53] text-white hover:bg-[#26a69a]" onClick={openAddUser}>
              + Aggiungi Utente
            </Button>
          </div>
          <div className="overflow-x-auto">
            {loading ? (
              <div className="text-gray-500 p-4">Caricamento utenti...</div>
            ) : errore ? (
              <div className="text-red-500 p-4">{errore}</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Username</TableHead>
                    <TableHead>Ruolo</TableHead>
                    <TableHead>IMEI</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {utenti.map((utente) => (
                    <TableRow key={utente.username}>
                      <TableCell>{utente.username}</TableCell>
                      <TableCell>
                        <span className={utente.role === "admin" ? "bg-yellow-300 text-gray-800 px-2 py-1 rounded-full text-xs font-semibold" : "bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs"}>
                          {utente.role}
                        </span>
                      </TableCell>
                      <TableCell>
                        {utente.imeis.join(", ")}
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline" className="mr-2" onClick={() => openEditUser(utente)}>Modifica</Button>
                        <Button size="sm" variant="destructive" onClick={() => setDeleteUser(utente.username)}>Elimina</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        </section>
        {/* Esportazione/Importazione Messaggi */}
        <section className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-700">Esporta/Importa Messaggi Satellitari</h2>
          <form className="flex flex-col md:flex-row md:items-end md:space-x-6 space-y-4 md:space-y-0" onSubmit={e => e.preventDefault()}>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Utente</label>
              <select className="border rounded px-3 py-2 w-48" value={utenteSelezionato} onChange={e => { setUtenteSelezionato(e.target.value); setImeiSelezionato(""); }}>
                <option value="">Tutti</option>
                {utenti.map(u => (
                  <option key={u.username} value={u.username}>{u.username}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">IMEI</label>
              <select className="border rounded px-3 py-2 w-48" value={imeiSelezionato} onChange={e => setImeiSelezionato(e.target.value)}>
                <option value="">Tutti</option>
                {imeiDisponibili.map(imei => (
                  <option key={imei} value={imei}>{imei}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Formato</label>
              <select className="border rounded px-3 py-2 w-32" value={formato} onChange={e => setFormato(e.target.value)}>
                <option value="json">JSON</option>
                <option value="kml">KML</option>
              </select>
            </div>
            <Button type="button" ref={exportBtnRef} className="bg-[#005c53] text-white hover:bg-[#26a69a]" onClick={handleExport}>Esporta</Button>
            <label className="inline-block cursor-pointer bg-gray-100 border border-gray-300 rounded px-3 py-2 text-sm text-gray-700 hover:bg-gray-200">
              <input type="file" accept=".json,.kml" className="hidden" onChange={handleImport} />
              Importa File
            </label>
          </form>
          <div className="mt-4 text-xs text-gray-500">Seleziona utente e IMEI per esportare o importare i messaggi in formato JSON o KML.</div>
        </section>

        {/* Riorganizzazione TripId */}
        <section className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-700">Riorganizzazione TripId</h2>
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              <p className="mb-2">Questa funzione riorganizza i messaggi esistenti per assegnare correttamente i tripId in ordine cronologico.</p>
              <p className="mb-4">Utile per correggere messaggi che sono stati assegnati al viaggio sbagliato a causa di problemi di sincronizzazione temporale.</p>
            </div>
            <div className="flex flex-col md:flex-row md:items-end md:space-x-4 space-y-4 md:space-y-0">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">IMEI specifico (opzionale)</label>
                <select 
                  className="border rounded px-3 py-2 w-48" 
                  value={imeiSelezionato} 
                  onChange={e => setImeiSelezionato(e.target.value)}
                >
                  <option value="">Tutti gli IMEI</option>
                  {imeiDisponibili.map(imei => (
                    <option key={imei} value={imei}>{imei}</option>
                  ))}
                </select>
              </div>
              <Button 
                type="button" 
                className="bg-orange-600 text-white hover:bg-orange-700" 
                onClick={async () => {
                  console.log('Riorganizza TripId clicked, IMEI:', imeiSelezionato);
                  try {
                    const payload = { imei: imeiSelezionato || undefined };
                    console.log('Sending payload:', payload);
                    
                    const res = await fetch('/api/admin/trips/reorganize', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify(payload),
                    });
                    
                    console.log('Response status:', res.status);
                    const data = await res.json();
                    console.log('Response data:', data);
                    
                    if (res.ok) {
                      toast({
                        title: "Riorganizzazione completata",
                        description: data.message,
                      });
                    } else {
                      console.error('Server error:', data);
                      toast({
                        title: "Errore di riorganizzazione",
                        description: data.message || 'Errore sconosciuto',
                        variant: "destructive",
                      });
                    }
                  } catch (err) {
                    console.error('Fetch error:', err);
                    toast({
                      title: "Errore di riorganizzazione",
                      description: 'Errore durante la riorganizzazione: ' + (err instanceof Error ? err.message : 'Errore sconosciuto'),
                      variant: "destructive",
                    });
                  }
                }}
              >
                Riorganizza TripId
              </Button>
            </div>
            <div className="text-xs text-gray-500">
              ⚠️ Questa operazione può richiedere alcuni secondi per completarsi.
            </div>
          </div>
        </section>
        {/* MODALE AGGIUNGI/MODIFICA UTENTE */}
        <Dialog open={showUserModal} onOpenChange={closeUserModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editUser ? 'Modifica Utente' : 'Aggiungi Utente'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleUserSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <Input value={formUser.username} onChange={e => handleFormChange('username', e.target.value)} disabled={!!editUser} required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password {editUser && <span className="text-xs text-gray-400">(lascia vuoto per non cambiare)</span>}</label>
                <Input type="password" value={formUser.password} onChange={e => handleFormChange('password', e.target.value)} required={!editUser} />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ruolo</label>
                <Select value={formUser.role} onValueChange={v => handleFormChange('role', v)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Ruolo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">user</SelectItem>
                    <SelectItem value="admin">admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">IMEI</label>
                {formUser.imeis.map((imei, idx) => (
                  <div key={idx} className="flex items-center space-x-2 mb-1">
                    <Input value={imei} onChange={e => handleFormChange('imeis', e.target.value, idx)} required className="w-56" />
                    {formUser.imeis.length > 1 && (
                      <Button type="button" size="sm" variant="ghost" onClick={() => handleFormChange('imeis-remove', '', idx)}>-</Button>
                    )}
                    {idx === formUser.imeis.length - 1 && (
                      <Button type="button" size="sm" variant="ghost" onClick={() => handleFormChange('imeis-add', '')}>+</Button>
                    )}
                  </div>
                ))}
              </div>
              {formError && <div className="text-red-600 text-sm">{formError}</div>}
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>{formLoading ? 'Salvataggio...' : 'Salva'}</Button>
                <Button type="button" variant="ghost" onClick={closeUserModal}>Annulla</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
        {/* DIALOG CONFERMA ELIMINAZIONE UTENTE */}
        <Dialog open={!!deleteUser} onOpenChange={() => setDeleteUser(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Conferma eliminazione</DialogTitle>
              <DialogDescription>
                Sei sicuro di voler eliminare l'utente <span className="font-semibold">{deleteUser}</span>?<br />Questa operazione non è reversibile.
              </DialogDescription>
            </DialogHeader>
            {deleteError && <div className="text-red-600 text-sm mb-2">{deleteError}</div>}
            <DialogFooter>
              <Button variant="destructive" onClick={handleDeleteUser} disabled={deleteLoading}>{deleteLoading ? 'Eliminazione...' : 'Elimina'}</Button>
              <Button variant="ghost" onClick={() => setDeleteUser(null)} disabled={deleteLoading}>Annulla</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
} 